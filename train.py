import os
import glob
import numpy as np
import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
from solution import SVDNet
from loss_functions import AEAlignedLoss, AdaptiveLoss, compute_ae_metric
import argparse
from tqdm import tqdm
import math
import copy

# ----------------------------------------------------------------------------
# Utility functions
# ----------------------------------------------------------------------------

def read_cfg_file(file_path: str):
    """Parse the cfg txt to obtain antenna dims, IQ channels and rank.
    Returns: (samp_num, M, N, IQ, R)
    """
    with open(file_path, "r") as f:
        lines = [line.strip() for line in f.readlines() if line.strip()]
    samp_num = int(lines[0])
    M = int(lines[1])
    N = int(lines[2])
    IQ = int(lines[3])
    R = int(lines[4])
    return samp_num, M, N, IQ, R


# ----------------------------------------------------------------------------
# Snapshot Ensemble Implementation
# ----------------------------------------------------------------------------

class CyclicCosineAnnealingLR:
    """Cyclic Cosine Annealing Learning Rate Scheduler for Snapshot Ensembles."""

    def __init__(self, optimizer, T_max, eta_min=0, cycles=5):
        self.optimizer = optimizer
        self.T_max = T_max  # Total epochs
        self.eta_min = eta_min
        self.cycles = cycles
        self.cycle_length = T_max // cycles
        self.base_lrs = [group['lr'] for group in optimizer.param_groups]
        self.current_cycle = 0
        self.cycle_epoch = 0

    def step(self, epoch):
        """Update learning rate based on current epoch."""
        self.current_cycle = epoch // self.cycle_length
        self.cycle_epoch = epoch % self.cycle_length

        # Cosine annealing within each cycle
        for param_group, base_lr in zip(self.optimizer.param_groups, self.base_lrs):
            param_group['lr'] = self.eta_min + (base_lr - self.eta_min) * \
                (1 + math.cos(math.pi * self.cycle_epoch / self.cycle_length)) / 2

    def is_snapshot_epoch(self, epoch):
        """Check if current epoch is a snapshot epoch (end of cycle)."""
        return (epoch + 1) % self.cycle_length == 0 and epoch > 0

    def get_last_lr(self):
        """Get current learning rates."""
        return [group['lr'] for group in self.optimizer.param_groups]


class SnapshotEnsemble:
    """Snapshot Ensemble manager for saving and loading model snapshots."""

    def __init__(self, save_dir="snapshots", max_snapshots=5):
        self.save_dir = save_dir
        self.max_snapshots = max_snapshots
        self.snapshots = []

        # Create save directory
        os.makedirs(save_dir, exist_ok=True)

    def save_snapshot(self, model, epoch, loss):
        """Save a model snapshot."""
        snapshot_path = os.path.join(self.save_dir, f"snapshot_epoch_{epoch}_loss_{loss:.6f}.pth")
        torch.save({
            'epoch': epoch,
            'model_state_dict': copy.deepcopy(model.state_dict()),
            'loss': loss
        }, snapshot_path)

        self.snapshots.append({
            'path': snapshot_path,
            'epoch': epoch,
            'loss': loss
        })

        # Keep only the best snapshots
        if len(self.snapshots) > self.max_snapshots:
            # Sort by loss and keep the best ones
            self.snapshots.sort(key=lambda x: x['loss'])
            worst_snapshot = self.snapshots.pop()
            if os.path.exists(worst_snapshot['path']):
                os.remove(worst_snapshot['path'])

        print(f"Saved snapshot at epoch {epoch} with loss {loss:.6f}")

    def load_snapshots(self):
        """Load all saved snapshots."""
        snapshot_files = glob.glob(os.path.join(self.save_dir, "snapshot_*.pth"))
        loaded_snapshots = []

        for snapshot_file in snapshot_files:
            snapshot_data = torch.load(snapshot_file, map_location='cpu')
            loaded_snapshots.append({
                'path': snapshot_file,
                'state_dict': snapshot_data['model_state_dict'],
                'epoch': snapshot_data['epoch'],
                'loss': snapshot_data['loss']
            })

        return loaded_snapshots

# ----------------------------------------------------------------------------
# Dataset definition (memory-mapped to keep RAM usage low)
# ----------------------------------------------------------------------------

class ChannelDataset(Dataset):
    def __init__(self, data_files, label_files):
        assert len(data_files) == len(label_files), "data/label file count mismatch"
        self.data_arrays = [np.load(f, mmap_mode="r") for f in data_files]
        self.label_arrays = [np.load(f, mmap_mode="r") for f in label_files]
        self.cumsum = np.cumsum([arr.shape[0] for arr in self.data_arrays])

    def __len__(self):
        return int(self.cumsum[-1])

    def __getitem__(self, idx):
        file_idx = int(np.searchsorted(self.cumsum, idx, side="right"))
        prev_cum = 0 if file_idx == 0 else self.cumsum[file_idx - 1]
        inner_idx = idx - prev_cum
        H_in = self.data_arrays[file_idx][inner_idx]     # shape [M, N, 2]
        H_gt = self.label_arrays[file_idx][inner_idx]    # ideal channel
        # numpy -> torch
        H_in = torch.from_numpy(H_in).float()
        H_gt = torch.from_numpy(H_gt).float()
        return H_in, H_gt

# ----------------------------------------------------------------------------
# Complex helpers
# ----------------------------------------------------------------------------

def to_complex(t):
    return torch.complex(t[..., 0], t[..., 1])

# ----------------------------------------------------------------------------
# Training loop
# ----------------------------------------------------------------------------

def frob_norm(t):
    """Compute Frobenius norm squared for complex tensor."""
    return torch.sum(torch.abs(t) ** 2, dim=(-2, -1))  # returns [...]


def apply_data_augmentation(H_in, H_gt):
    """Apply data augmentation strategies as described in the solution document.

    Args:
        H_in: Input noisy channel [B, M, N, 2]
        H_gt: Ground truth ideal channel [B, M, N, 2]

    Returns:
        H_in_aug: Augmented input channel
        H_gt: Ground truth channel (unchanged)
    """
    # Convert to complex for augmentation
    H_in_c = to_complex(H_in)

    # 1. Additional noise injection
    noise_std = 0.01  # Small additional noise
    noise = torch.randn_like(H_in_c) * noise_std
    H_in_c = H_in_c + noise

    # 2. Random phase rotation
    batch_size = H_in_c.shape[0]
    theta = torch.rand(batch_size, 1, 1, device=H_in_c.device) * 2 * torch.pi
    phase_rotation = torch.exp(1j * theta)
    H_in_c = H_in_c * phase_rotation

    # Convert back to real-imaginary format
    H_in_aug = torch.stack([H_in_c.real, H_in_c.imag], dim=-1)

    return H_in_aug, H_gt


def apply_enhanced_data_augmentation(H_in, H_gt):
    """Enhanced data augmentation with more sophisticated techniques.

    Args:
        H_in: Input noisy channel [B, M, N, 2]
        H_gt: Ground truth ideal channel [B, M, N, 2]

    Returns:
        H_in_aug: Augmented input channel
        H_gt: Ground truth channel (unchanged)
    """
    # Convert to complex for augmentation
    H_in_c = to_complex(H_in)
    batch_size = H_in_c.shape[0]

    # 1. Adaptive noise injection based on signal strength
    signal_power = torch.mean(torch.abs(H_in_c) ** 2, dim=(-2, -1), keepdim=True)
    noise_std = 0.005 + 0.01 * torch.sqrt(signal_power)  # Adaptive noise level
    noise = torch.randn_like(H_in_c) * noise_std
    H_in_c = H_in_c + noise

    # 2. Random phase rotation (global)
    theta = torch.rand(batch_size, 1, 1, device=H_in_c.device) * 2 * torch.pi
    phase_rotation = torch.exp(1j * theta)
    H_in_c = H_in_c * phase_rotation

    # 3. Random conjugate transpose (with probability 0.5)
    if torch.rand(1).item() > 0.5:
        H_in_c = torch.conj(H_in_c.transpose(-2, -1))

    # 4. Small random scaling
    scale_factor = 1.0 + 0.05 * (torch.rand(batch_size, 1, 1, device=H_in_c.device) - 0.5)
    H_in_c = H_in_c * scale_factor

    # Convert back to real-imaginary format
    H_in_aug = torch.stack([H_in_c.real, H_in_c.imag], dim=-1)

    return H_in_aug, H_gt


def train_adaptive_svd(model, dataloader, device="cpu", lr=2e-4, epochs=150, weight_decay=1e-5,
                      lambda_rec=1.0, lambda_orth_u=1.0, lambda_orth_v=1.0,
                      warmup_epochs=10, use_cosine_schedule=True):
    """
    Training function optimized for the new AdaptiveSVDNet architecture.

    Key improvements:
    - Higher learning rate suitable for the new architecture
    - Balanced loss weights for reconstruction and orthogonality
    - Warmup schedule for stable training
    - Cosine annealing for better convergence
    - Simplified data augmentation focused on channel properties
    """
    model.to(device)

    # Use AdamW with higher learning rate for the new architecture
    opt = torch.optim.AdamW(model.parameters(), lr=lr, weight_decay=weight_decay,
                           betas=(0.9, 0.95))  # Slightly different betas for better convergence

    # Learning rate scheduler with warmup
    if use_cosine_schedule:
        def lr_lambda(epoch):
            if epoch < warmup_epochs:
                return epoch / warmup_epochs
            else:
                return 0.5 * (1 + math.cos(math.pi * (epoch - warmup_epochs) / (epochs - warmup_epochs)))
        scheduler = torch.optim.lr_scheduler.LambdaLR(opt, lr_lambda)
    else:
        scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(opt, mode='min', factor=0.5, patience=10)

    # Use AE-aligned loss with balanced weights
    loss_fn = AEAlignedLoss(
        lambda_rec=lambda_rec,
        lambda_orth_u=lambda_orth_u,
        lambda_orth_v=lambda_orth_v,
        use_normalized_orth=True
    ).to(device)
    print(f"[Training] Using AE-aligned loss with weights: rec={lambda_rec}, orth_u={lambda_orth_u}, orth_v={lambda_orth_v}")

    # Training metrics tracking
    best_ae = float('inf')
    patience_counter = 0
    patience = 20

    for ep in range(1, epochs + 1):
        model.train()
        total_loss = 0.0
        total_recon_loss = 0.0
        total_orth_loss = 0.0
        total_ae = 0.0
        num_batches = 0

        prog_bar = tqdm(dataloader, desc=f"Epoch {ep}/{epochs}")

        for H_in, H_gt in prog_bar:
            H_in = H_in.to(device)
            H_gt = H_gt.to(device)

            # Apply channel-aware data augmentation (lighter than before)
            H_in_aug, H_gt = apply_channel_augmentation(H_in, H_gt)

            # Forward pass
            U, S, V = model(H_in_aug)

            # Ensure batch dim for loss computation
            if U.ndim == 3:  # squeezed when batch==1
                U = U.unsqueeze(0)
                V = V.unsqueeze(0)
                S = S.unsqueeze(0)

            # Compute loss
            loss_dict = loss_fn(U, S, V, H_gt)
            total_loss_batch = loss_dict['total_loss']
            recon_loss = loss_dict['reconstruction_loss']
            orth_U = loss_dict['orthogonality_loss_u']
            orth_V = loss_dict['orthogonality_loss_v']

            # Compute AE metric for monitoring
            with torch.no_grad():
                ae_metric = compute_ae_metric(U, S, V, H_gt)

            # Optimization step
            opt.zero_grad()
            total_loss_batch.backward()

            # Gradient clipping (more conservative for new architecture)
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=0.5)

            opt.step()

            # Accumulate metrics
            batch_size = H_in.size(0)
            total_loss += total_loss_batch.item() * batch_size
            total_recon_loss += recon_loss.item() * batch_size
            total_orth_loss += (orth_U.item() + orth_V.item()) * batch_size
            total_ae += ae_metric.item() * batch_size
            num_batches += batch_size

            # Update progress bar
            current_lr = opt.param_groups[0]['lr']
            prog_bar.set_postfix({
                "loss": f"{total_loss_batch.item():.4f}",
                "recon": f"{recon_loss.item():.4f}",
                "orth": f"{(orth_U.item() + orth_V.item()):.2e}",
                "ae": f"{ae_metric.item():.4f}",
                "lr": f"{current_lr:.2e}"
            })

        # Update learning rate
        if use_cosine_schedule:
            scheduler.step()

        # Compute average metrics
        avg_loss = total_loss / num_batches
        avg_recon_loss = total_recon_loss / num_batches
        avg_orth_loss = total_orth_loss / num_batches
        avg_ae = total_ae / num_batches

        # Update scheduler if using ReduceLROnPlateau
        if not use_cosine_schedule:
            scheduler.step(avg_ae)

        print(f"[Summary] Epoch {ep}/{epochs} | "
              f"Loss={avg_loss:.6f} | "
              f"Recon={avg_recon_loss:.6f} | "
              f"Orth={avg_orth_loss:.2e} | "
              f"AE={avg_ae:.6f} | "
              f"LR={opt.param_groups[0]['lr']:.2e}")

        # Early stopping based on AE metric
        if avg_ae < best_ae:
            best_ae = avg_ae
            patience_counter = 0
            # Save best model
            torch.save(model.state_dict(), "best_adaptive_svd.pth")
            print(f"New best AE: {best_ae:.6f} - Model saved")
        else:
            patience_counter += 1
            if patience_counter >= patience:
                print(f"Early stopping triggered after {ep} epochs")
                break

    return model


def apply_channel_augmentation(H_in, H_gt, noise_std=0.005, rotation_prob=0.2,
                              scaling_prob=0.1, phase_shift_prob=0.3):
    """
    Channel-aware data augmentation optimized for the new architecture.

    Lighter augmentation strategy:
    1. Reduced noise levels for better convergence
    2. Lower probability of geometric transformations
    3. Focus on realistic channel impairments
    """
    device = H_in.device
    B, M, N, _ = H_in.shape

    # Convert to complex for easier manipulation
    H_in_c = torch.complex(H_in[..., 0], H_in[..., 1])
    H_gt_c = torch.complex(H_gt[..., 0], H_gt[..., 1])

    # 1. Light additive complex Gaussian noise
    if torch.rand(1).item() < 0.6:  # Reduced probability
        noise_real = torch.randn_like(H_in_c.real) * noise_std
        noise_imag = torch.randn_like(H_in_c.imag) * noise_std
        noise = torch.complex(noise_real, noise_imag)
        H_in_c = H_in_c + noise

    # 2. Occasional unitary rotations (preserve SVD structure)
    if torch.rand(1).item() < rotation_prob:
        # Generate smaller random unitary matrices for stability
        U_left = generate_random_unitary(M, device, scale=0.1)
        U_right = generate_random_unitary(N, device, scale=0.1)

        # Apply rotations
        H_in_c = torch.matmul(U_left, torch.matmul(H_in_c, U_right.conj().T))
        H_gt_c = torch.matmul(U_left, torch.matmul(H_gt_c, U_right.conj().T))

    # 3. Mild power scaling
    if torch.rand(1).item() < scaling_prob:
        scale_factor = torch.exp(torch.randn(B, 1, 1, device=device) * 0.1)  # Smaller variance
        H_in_c = H_in_c * scale_factor
        H_gt_c = H_gt_c * scale_factor

    # 4. Phase shifts (common in wireless systems)
    if torch.rand(1).item() < phase_shift_prob:
        phase_shift = torch.rand(B, 1, 1, device=device) * 2 * torch.pi
        phase_factor = torch.exp(1j * phase_shift)
        H_in_c = H_in_c * phase_factor
        H_gt_c = H_gt_c * phase_factor

    # Convert back to real-imaginary format
    H_in_aug = torch.stack([H_in_c.real, H_in_c.imag], dim=-1)
    H_gt_aug = torch.stack([H_gt_c.real, H_gt_c.imag], dim=-1)

    return H_in_aug, H_gt_aug


def generate_random_unitary(n, device, scale=1.0):
    """Generate a random unitary matrix using QR decomposition."""
    # Generate random complex matrix
    A = torch.randn(n, n, device=device, dtype=torch.complex64) * scale
    # QR decomposition to get unitary matrix
    Q, R = torch.linalg.qr(A)
    # Ensure proper phase
    d = torch.diagonal(R)
    ph = d / torch.abs(d)
    Q = Q * ph.unsqueeze(0)
    return Q


# ----------------------------------------------------------------------------
# Main entry
# ----------------------------------------------------------------------------

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Train Ortho-Efficient SVDNet on provided channel data")
    parser.add_argument("--data_dir", type=str, default="CompetitionData1", help="Directory containing Round1TrainData*.npy")
    parser.add_argument("--device", type=str, default="cuda" if torch.cuda.is_available() else "cpu", help="Training device (cuda/cpu)")
    parser.add_argument("--batch_size", type=int, default=128, help="Batch size (64/128 recommended)")
    parser.add_argument("--epochs", type=int, default=200, help="Training epochs (200-300 recommended)")
    parser.add_argument("--lr", type=float, default=1e-5, help="Initial learning rate")
    parser.add_argument("--weight_decay", type=float, default=1e-4, help="Weight decay for regularization")
    parser.add_argument("--num_neg_ones_in_D", type=int, default=0,
                       help="Number of -1 elements in D matrix for Cayley transform")
    parser.add_argument("--learnable_D", action="store_true",
                       help="Make D matrix learnable in Cayley transform")
    parser.add_argument("--use_adaptive_loss", action="store_true", default=True,
                       help="Use adaptive loss function with learnable weights")
    args = parser.parse_args()

    # Detect training files (Round1)
    data_files = sorted(glob.glob(os.path.join(args.data_dir, "Round1TrainData*.npy")))
    label_files = sorted(glob.glob(os.path.join(args.data_dir, "Round1TrainLabel*.npy")))
    cfg_file = os.path.join(args.data_dir, "Round1CfgData1.txt")  # assume same dims for all
    if not data_files or not label_files:
        raise FileNotFoundError("Training *.npy files not found under given data_dir")

    _, M, N, IQ, R = read_cfg_file(cfg_file)
    print(f"Found {len(data_files)} training files, each dim=({M},{N}), R={R}, device={args.device}")
    print(f"Training Ortho-Efficient SVDNet with Scaled Cayley Transform for hard orthogonality constraints")

    ds = ChannelDataset(data_files, label_files)
    loader = DataLoader(ds, batch_size=args.batch_size, shuffle=True, num_workers=0)

    # Initialize the enhanced SVDNet with configurable D matrix
    model = SVDNet(
        dim=M,
        rank=R,
        weight_path="svdnet_final.pth",
        num_neg_ones_in_D=args.num_neg_ones_in_D,
        learnable_D=args.learnable_D
    )
    print(f"Enhanced model initialized with {sum(p.numel() for p in model.parameters())} parameters")
    print(f"D matrix configuration: num_neg_ones={args.num_neg_ones_in_D}, learnable={args.learnable_D}")

    # Train the model with the new adaptive architecture
    trained_model = train_adaptive_svd(
        model, loader,
        device=args.device,
        lr=2e-4,  # Higher learning rate for new architecture
        epochs=args.epochs,
        weight_decay=1e-5,  # Lower weight decay
        lambda_rec=1.0,
        lambda_orth_u=1.0,  # Balanced orthogonality weights
        lambda_orth_v=1.0,
        warmup_epochs=10,
        use_cosine_schedule=True
    )

    # Save final trained weights
    torch.save(trained_model.state_dict(), "adaptive_svd_final.pth")
    print("Final model saved as adaptive_svd_final.pth")
    print("Best model saved as best_adaptive_svd.pth during training")

    print("Training with snapshot ensembles completed successfully!")