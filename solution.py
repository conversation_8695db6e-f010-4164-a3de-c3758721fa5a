import os
import torch
import torch.nn as nn
import torch.nn.functional as F
import math

# -----------------------------------------------------------------------------
# Matrix-Aware Neural SVD Components
# -----------------------------------------------------------------------------

class MatrixDenoiser(nn.Module):
    """Matrix-aware denoising module that preserves channel matrix structure."""

    def __init__(self, dim, hidden_dim=128):
        super().__init__()
        self.dim = dim

        # Row-wise processing (receiver antennas)
        self.row_processor = nn.Sequential(
            nn.Linear(dim * 2, hidden_dim),  # *2 for complex (real, imag)
            nn.LayerNorm(hidden_dim),
            nn.GELU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.Layer<PERSON>(hidden_dim),
            nn.<PERSON>(),
            nn.Linear(hidden_dim, dim * 2)
        )

        # Column-wise processing (transmitter antennas)
        self.col_processor = nn.Sequential(
            nn.Linear(dim * 2, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.GELU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.GELU(),
            nn.Linear(hidden_dim, dim * 2)
        )

        # Cross-antenna attention
        self.cross_attention = nn.MultiheadAttention(
            embed_dim=hidden_dim, num_heads=8, batch_first=True
        )

        # Final fusion
        self.fusion = nn.Sequential(
            nn.Linear(dim * 2 * 2, dim * 2),  # Combine row and col features
            nn.Tanh()  # Bounded output for stability
        )

    def forward(self, H):
        """
        Args:
            H: [B, M, N, 2] noisy channel matrix
        Returns:
            H_clean: [B, M, N, 2] denoised channel matrix
        """
        B, M, N, _ = H.shape

        # Flatten complex dimension for processing
        H_flat = H.view(B, M, N * 2)  # [B, M, N*2]

        # Row-wise processing (each receiver antenna)
        row_features = []
        for i in range(M):
            row_i = H_flat[:, i, :]  # [B, N*2]
            row_processed = self.row_processor(row_i)  # [B, N*2]
            row_features.append(row_processed)
        row_features = torch.stack(row_features, dim=1)  # [B, M, N*2]

        # Column-wise processing (each transmitter antenna)
        H_flat_T = H_flat.transpose(1, 2)  # [B, N*2, M]
        col_features = []
        for j in range(N):
            col_j = H_flat_T[:, j*2:(j+1)*2, :].flatten(1)  # [B, M*2]
            col_processed = self.col_processor(col_j)  # [B, M*2]
            col_features.append(col_processed.view(B, M, 2))
        col_features = torch.stack(col_features, dim=2)  # [B, M, N, 2]
        col_features = col_features.view(B, M, N * 2)  # [B, M, N*2]

        # Combine row and column features
        combined = torch.cat([row_features, col_features], dim=-1)  # [B, M, N*2*2]

        # Final fusion
        H_delta = self.fusion(combined)  # [B, M, N*2]
        H_delta = H_delta.view(B, M, N, 2)  # [B, M, N, 2]

        # Residual connection
        H_clean = H + 0.1 * H_delta  # Small residual for stability

        return H_clean


class PowerIterationBlock(nn.Module):
    """Neural power iteration block for SVD approximation."""

    def __init__(self, dim, rank, hidden_dim=64):
        super().__init__()
        self.dim = dim
        self.rank = rank

        # Learnable refinement for U update
        self.u_refiner = nn.Sequential(
            nn.Linear(dim * 2, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.GELU(),
            nn.Linear(hidden_dim, dim * 2),
            nn.Tanh()
        )

        # Learnable refinement for V update
        self.v_refiner = nn.Sequential(
            nn.Linear(dim * 2, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.GELU(),
            nn.Linear(hidden_dim, dim * 2),
            nn.Tanh()
        )

        # Singular value predictor
        self.s_predictor = nn.Sequential(
            nn.Linear(4, hidden_dim),  # u^H @ H @ v gives scalar, but we use 4 features
            nn.GELU(),
            nn.Linear(hidden_dim, 1),
            nn.Softplus()
        )

    def forward(self, H_c, U_prev, V_prev, S_prev):
        """
        Args:
            H_c: [B, M, N] complex channel matrix
            U_prev: [B, M, R] complex previous U
            V_prev: [B, N, R] complex previous V
            S_prev: [B, R] previous singular values
        Returns:
            U_new, V_new, S_new: Updated SVD components
        """
        B, M, N = H_c.shape
        R = self.rank

        U_new_list = []
        V_new_list = []
        S_new_list = []

        for r in range(R):
            u_r = U_prev[:, :, r]  # [B, M]
            v_r = V_prev[:, :, r]  # [B, N]

            # Power iteration updates
            # v_new = H^H @ u / ||H^H @ u||
            Hh_u = torch.matmul(H_c.conj().transpose(-2, -1), u_r.unsqueeze(-1)).squeeze(-1)  # [B, N]

            # Neural refinement
            Hh_u_ri = torch.stack([Hh_u.real, Hh_u.imag], dim=-1)  # [B, N, 2]
            Hh_u_ri_flat = Hh_u_ri.view(B, N * 2)  # [B, N*2]
            v_delta = self.v_refiner(Hh_u_ri_flat)  # [B, N*2]
            v_delta = v_delta.view(B, N, 2)  # [B, N, 2]
            v_delta_c = torch.complex(v_delta[..., 0], v_delta[..., 1])  # [B, N]

            v_new = Hh_u + 0.1 * v_delta_c  # Small neural correction
            v_new = F.normalize(v_new, p=2, dim=-1)  # Normalize

            # u_new = H @ v / ||H @ v||
            H_v = torch.matmul(H_c, v_new.unsqueeze(-1)).squeeze(-1)  # [B, M]

            # Neural refinement
            H_v_ri = torch.stack([H_v.real, H_v.imag], dim=-1)  # [B, M, 2]
            H_v_ri_flat = H_v_ri.view(B, M * 2)  # [B, M*2]
            u_delta = self.u_refiner(H_v_ri_flat)  # [B, M*2]
            u_delta = u_delta.view(B, M, 2)  # [B, M, 2]
            u_delta_c = torch.complex(u_delta[..., 0], u_delta[..., 1])  # [B, M]

            u_new = H_v + 0.1 * u_delta_c  # Small neural correction
            u_new = F.normalize(u_new, p=2, dim=-1)  # Normalize

            # Compute singular value: s = u^H @ H @ v
            s_new = torch.matmul(u_new.conj().unsqueeze(1),
                               torch.matmul(H_c, v_new.unsqueeze(-1))).squeeze(-1).squeeze(-1)

            # Neural refinement for singular value
            s_features = torch.stack([
                s_new.real, s_new.imag,
                torch.abs(s_new), torch.angle(s_new)
            ], dim=-1)  # [B, 4]
            s_refined = self.s_predictor(s_features).squeeze(-1)  # [B]

            U_new_list.append(u_new)
            V_new_list.append(v_new)
            S_new_list.append(s_refined)

            # Deflation: remove current component from H
            H_c = H_c - s_refined.unsqueeze(-1).unsqueeze(-1) * torch.matmul(
                u_new.unsqueeze(-1), v_new.conj().unsqueeze(1)
            )

        U_new = torch.stack(U_new_list, dim=-1)  # [B, M, R]
        V_new = torch.stack(V_new_list, dim=-1)  # [B, N, R]
        S_new = torch.stack(S_new_list, dim=-1)  # [B, R]

        return U_new, V_new, S_new


class OrthogonalProjection(nn.Module):
    """Learnable orthogonal projection using Gram-Schmidt process."""

    def __init__(self, dim, rank):
        super().__init__()
        self.dim = dim
        self.rank = rank

    def forward(self, X):
        """
        Apply Gram-Schmidt orthogonalization to columns of X.
        Args:
            X: [B, dim, rank] complex matrix
        Returns:
            Q: [B, dim, rank] orthogonal matrix
        """
        B, dim, rank = X.shape
        Q = torch.zeros_like(X)

        for j in range(rank):
            v = X[:, :, j]  # [B, dim]

            # Subtract projections onto previous vectors
            for i in range(j):
                q_i = Q[:, :, i]  # [B, dim]
                proj = torch.sum(v.conj() * q_i, dim=1, keepdim=True) * q_i  # [B, dim]
                v = v - proj

            # Normalize
            norm = torch.norm(v, p=2, dim=1, keepdim=True)  # [B, 1]
            Q[:, :, j] = v / (norm + 1e-8)

        return Q


class AdaptiveSVDNet(nn.Module):
    """
    Adaptive SVD Network that learns to perform SVD through iterative refinement.

    Key innovations:
    1. Matrix-aware denoising that preserves channel structure
    2. Neural power iteration with learnable refinements
    3. Orthogonal projection to ensure unitary constraints
    4. Adaptive iteration count based on convergence
    """

    def __init__(self, dim=64, rank=32, num_iterations=5, hidden_dim=128):
        super().__init__()
        self.dim = dim
        self.rank = rank
        self.num_iterations = num_iterations

        # Matrix denoiser
        self.denoiser = MatrixDenoiser(dim, hidden_dim)

        # Power iteration blocks
        self.power_blocks = nn.ModuleList([
            PowerIterationBlock(dim, rank, hidden_dim)
            for _ in range(num_iterations)
        ])

        # Orthogonal projection
        self.orth_proj_U = OrthogonalProjection(dim, rank)
        self.orth_proj_V = OrthogonalProjection(dim, rank)

        # Convergence predictor
        self.convergence_net = nn.Sequential(
            nn.Linear(rank * 3, hidden_dim),  # 3 = current + prev + diff
            nn.GELU(),
            nn.Linear(hidden_dim, 1),
            nn.Sigmoid()
        )

        # Initialize with random orthogonal matrices
        self.register_buffer('init_U', self._random_orthogonal(dim, rank))
        self.register_buffer('init_V', self._random_orthogonal(dim, rank))

    def _random_orthogonal(self, m, n):
        """Generate random orthogonal matrix."""
        A = torch.randn(m, n, dtype=torch.complex64)
        Q, _ = torch.linalg.qr(A)
        return Q

    def _to_complex(self, x):
        """Convert [B, M, N, 2] to [B, M, N] complex."""
        return torch.complex(x[..., 0], x[..., 1])

    def _to_real_imag(self, x):
        """Convert [B, M, N] complex to [B, M, N, 2]."""
        return torch.stack([x.real, x.imag], dim=-1)

    def forward(self, H):
        """
        Args:
            H: [B, M, N, 2] or [M, N, 2] noisy channel matrix
        Returns:
            U: [B, M, R, 2] or [M, R, 2] left unitary matrix
            S: [B, R] or [R] singular values
            V: [B, N, R, 2] or [N, R, 2] right unitary matrix
        """
        # Handle single sample case
        if H.ndim == 3:
            H = H.unsqueeze(0)
            squeeze_output = True
        else:
            squeeze_output = False

        B, M, N, _ = H.shape

        # Step 1: Matrix-aware denoising
        H_clean = self.denoiser(H)  # [B, M, N, 2]
        H_c = self._to_complex(H_clean)  # [B, M, N]

        # Step 2: Initialize with random orthogonal matrices
        U = self.init_U.unsqueeze(0).expand(B, -1, -1).clone()  # [B, M, R]
        V = self.init_V.unsqueeze(0).expand(B, -1, -1).clone()  # [B, N, R]
        S = torch.ones(B, self.rank, device=H.device)  # [B, R]

        # Step 3: Iterative refinement with adaptive stopping
        prev_S = S.clone()

        for i, power_block in enumerate(self.power_blocks):
            U, V, S = power_block(H_c, U, V, S)

            # Apply orthogonal projection to maintain constraints
            U = self.orth_proj_U(U)
            V = self.orth_proj_V(V)

            # Check convergence (except for last iteration)
            if i < len(self.power_blocks) - 1:
                s_diff = torch.abs(S - prev_S)
                convergence_features = torch.cat([
                    S, prev_S, s_diff
                ], dim=-1)  # [B, R*3]

                convergence_score = self.convergence_net(convergence_features)  # [B, 1]

                # Early stopping if converged (in practice, continue for stability)
                prev_S = S.clone()

        # Step 4: Final orthogonalization and sorting
        U = self.orth_proj_U(U)
        V = self.orth_proj_V(V)

        # Sort by singular values (descending)
        S_sorted, indices = torch.sort(S, dim=-1, descending=True)
        U_sorted = torch.gather(U, -1, indices.unsqueeze(1).expand(-1, M, -1))
        V_sorted = torch.gather(V, -1, indices.unsqueeze(1).expand(-1, N, -1))

        # Convert back to real-imaginary format
        U_ri = self._to_real_imag(U_sorted)  # [B, M, R, 2]
        V_ri = self._to_real_imag(V_sorted)  # [B, N, R, 2]

        # Handle single sample case
        if squeeze_output:
            U_ri = U_ri.squeeze(0)
            V_ri = V_ri.squeeze(0)
            S_sorted = S_sorted.squeeze(0)

        return U_ri, S_sorted, V_ri


# -----------------------------------------------------------------------------
# Main SVDNet Class (Competition Interface)
# -----------------------------------------------------------------------------
class SVDNet(nn.Module):
    """
    Competition-compliant SVDNet using the new AdaptiveSVDNet architecture.

    This class provides the required interface while using the mathematically
    principled AdaptiveSVDNet internally.
    """

    def __init__(self, dim: int = 64, rank: int = 32, weight_path: str = "svdnet.pth",
                 num_neg_ones_in_D: int = 0, learnable_D: bool = False):
        super().__init__()
        self.dim = dim
        self.rank = rank

        # Use the new adaptive SVD architecture
        self.svd_net = AdaptiveSVDNet(
            dim=dim,
            rank=rank,
            num_iterations=6,  # Reasonable number of iterations
            hidden_dim=min(128, dim * 2)  # Adaptive hidden dimension
        )

        # Load weights if available
        if os.path.isfile(weight_path):
            try:
                state = torch.load(weight_path, map_location="cpu")
                self.load_state_dict(state, strict=False)
                print(f"[AdaptiveSVDNet] Loaded weights from {weight_path}")
            except Exception as e:
                print(f"[AdaptiveSVDNet] Weight loading failed: {e}. Using random initialization.")
        else:
            print(f"[AdaptiveSVDNet] No weights found at {weight_path}. Using random initialization.")

    def forward(self, x: torch.Tensor):
        """
        Forward pass compatible with competition requirements.

        Args:
            x: Channel matrix [M, N, 2] or [B, M, N, 2]
        Returns:
            U: Left unitary matrix [M, R, 2] or [B, M, R, 2]
            S: Singular values [R] or [B, R]
            V: Right unitary matrix [N, R, 2] or [B, N, R, 2]
        """
        return self.svd_net(x)

    @staticmethod
    def _to_complex(mat: torch.Tensor) -> torch.Tensor:
        """Convert real-imag stacked tensor [..., 2] → complex."""
        return torch.complex(mat[..., 0], mat[..., 1])

    @staticmethod
    def _to_ri(mat: torch.Tensor) -> torch.Tensor:
        """Convert complex tensor → real-imag stacked."""
        return torch.stack((mat.real, mat.imag), dim=-1)

    def get_model_complexity(self):
        """Calculate model complexity in terms of parameters and FLOPs."""
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)

        return {
            'total_params': total_params,
            'trainable_params': trainable_params,
            'model_size_mb': total_params * 4 / (1024 * 1024)  # Assuming float32
        }


